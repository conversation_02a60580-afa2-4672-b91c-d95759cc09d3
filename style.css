/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #1a1a1a;
    color: #e0e0e0;
    overflow: hidden;
}

.app-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

/* Canvas styles */
#canvas {
    display: block;
    background-color: #2a2a2a;
    border: 1px solid #404040;
    cursor: grab;
    width: 100%;
    height: 100%;
}

#canvas:active {
    cursor: grabbing;
}



/* Floating toolbar */
.floating-toolbar {
    position: fixed;
    top: 20px;
    left: 200px;
    background: #333333;
    border: 1px solid #505050;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    padding: 12px;
    display: flex;
    gap: 8px;
    z-index: 1000;
}

.toolbar-btn {
    background: #0d7377;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.toolbar-btn:hover {
    background: #14a085;
}

.toolbar-btn:active {
    transform: translateY(1px);
}

.toolbar-btn:disabled {
    background: #555555;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Canvas navigation controls */
.canvas-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #333333;
    border: 1px solid #505050;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    z-index: 1000;
}

.control-btn {
    background: #444444;
    color: #e0e0e0;
    border: 1px solid #666666;
    border-radius: 4px;
    width: 36px;
    height: 36px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.control-btn:hover {
    background: #555555;
    border-color: #777777;
}

.control-btn:active {
    transform: translateY(1px);
}

.control-separator {
    height: 1px;
    background-color: #666666;
    margin: 4px 0;
}

.control-btn:disabled {
    background-color: #2a2a2a;
    color: #666666;
    cursor: not-allowed;
    opacity: 0.6;
}

.control-btn:disabled:hover {
    background-color: #2a2a2a;
    border-color: #444444;
}

/* Node styles (for reference, actual nodes are drawn on canvas) */
.node-input {
    position: absolute;
    border: 2px solid #0d7377;
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
    font-family: inherit;
    background: #2a2a2a;
    color: #e0e0e0;
    outline: none;
    z-index: 2000;
}

/* Generate Ideas tooltip */
.generate-ideas-tooltip {
    position: absolute;
    z-index: 2000;
    pointer-events: none;
    transition: opacity 0.2s ease;
    display: flex;
    gap: 8px;
}

.generate-ideas-tooltip.hidden {
    display: none;
}

.generate-ideas-btn, .expand-content-btn {
    background: #0d7377;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    pointer-events: auto;
    position: relative;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.expand-content-btn {
    background: #6c5ce7;
}

.generate-ideas-btn:hover {
    background: #14a085;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

.expand-content-btn:hover {
    background: #a29bfe;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

.generate-ideas-btn:active, .expand-content-btn:active {
    transform: translateY(0);
}

.generate-ideas-btn:disabled {
    background: #555555;
    cursor: not-allowed;
    opacity: 0.7;
}

/* Tooltip arrow - only show on the first button */
.generate-ideas-btn::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #0d7377;
}

.generate-ideas-btn:hover::after {
    border-top-color: #14a085;
}

/* Content Modal */
.content-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-modal.hidden {
    display: none;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
}

.modal-container {
    position: relative;
    background: #2a2a2a;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    max-width: 80vw;
    max-height: 80vh;
    width: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #444;
    background: #333;
}

.modal-header h3 {
    margin: 0;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
}

.close-modal-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.close-modal-btn:hover {
    background: #555;
}

.modal-body {
    flex: 1;
    overflow: auto;
    padding: 24px;
}

.modal-content-text {
    color: #ffffff;
    font-size: 16px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* Modal content HTML styling */
.modal-content-text h1,
.modal-content-text h2,
.modal-content-text h3 {
    color: #ffffff;
    margin: 0 0 16px 0;
    font-weight: bold;
}

.modal-content-text h1 {
    font-size: 24px;
}

.modal-content-text h2 {
    font-size: 20px;
}

.modal-content-text h3 {
    font-size: 18px;
}

.modal-content-text p {
    margin: 0 0 12px 0;
    color: #ffffff;
}

.modal-content-text ul {
    margin: 0 0 12px 0;
    padding-left: 20px;
}

.modal-content-text li {
    margin: 4px 0;
    color: #ffffff;
}

.modal-content-text strong {
    font-weight: bold;
    color: #ffffff;
}

.modal-content-text em {
    font-style: italic;
    color: #ffffff;
}

.modal-content-text code {
    background: #444;
    color: #ffd700;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

/* Selection status indicator */
.selection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.selection-status.hidden {
    display: none;
}

.selection-hint {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #9ca3af;
    font-weight: normal;
}

/* Navigation help */
.navigation-help {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 1500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: opacity 0.3s ease;
}

.help-item {
    margin: 4px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.navigation-help:hover {
    opacity: 0.7;
}

/* Utility classes */
.hidden {
    display: none;
}

.no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Red arrows for undo/redo buttons */
#undoBtn,
#redoBtn {
    color: #ff4444;
}

#undoBtn:disabled,
#redoBtn:disabled {
    color: #884444;
}

/* API Key Modal */
.api-key-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: #333333;
    border: 1px solid #505050;
    border-radius: 12px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
}

.modal-content h3 {
    color: #e0e0e0;
    margin-bottom: 12px;
    font-size: 18px;
    font-weight: 600;
}

.modal-content p {
    color: #b0b0b0;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
}

.modal-content label {
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
    margin-top: 12px;
    display: block;
}

.modal-content label:first-of-type {
    margin-top: 0;
}

.modal-content input {
    width: 100%;
    padding: 12px;
    border: 1px solid #666666;
    border-radius: 6px;
    background: #2a2a2a;
    color: #e0e0e0;
    font-size: 14px;
    margin-bottom: 16px;
    outline: none;
    transition: border-color 0.2s ease;
}

.modal-content input:focus {
    border-color: #0d7377;
}

.modal-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
}

.modal-buttons button {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.modal-buttons button:first-child {
    background: #0d7377;
    color: #ffffff;
}

.modal-buttons button:first-child:hover {
    background: #14a085;
}

.modal-buttons button:last-child {
    background: #666666;
    color: #e0e0e0;
}

.modal-buttons button:last-child:hover {
    background: #777777;
}

.modal-content small {
    color: #888888;
    font-size: 12px;
}

/* Demo Mode Section */
.demo-mode-section {
    background: #2a2a2a;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.demo-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 8px;
}

.demo-toggle input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.demo-toggle-text {
    color: #e0e0e0;
    font-weight: 500;
    font-size: 14px;
}

.demo-description {
    color: #b0b0b0;
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

#apiConfigSection {
    transition: opacity 0.3s ease;
}

#apiConfigSection[style*="none"] {
    opacity: 0;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background: #0d7377;
    color: #ffffff;
}

.notification-error {
    background: #dc3545;
    color: #ffffff;
}

.notification-warning {
    background: #ffc107;
    color: #000000;
}

.notification-info {
    background: #17a2b8;
    color: #ffffff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}