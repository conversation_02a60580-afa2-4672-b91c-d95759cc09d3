# Markdown Test Examples

Here are some examples you can copy and paste into nodes to test the markdown functionality:

## Example 1: Headers
```
# Main Title
## Subtitle
### Small Header
Regular text below
```

## Example 2: Text Formatting
```
This is **bold text** and this is *italic text*.
You can also use `inline code` for technical terms.
```

## Example 3: Lists
```
# Shopping List
- Apples
- **Bananas** (organic)
- `Coffee` beans
- *Fresh* bread
```

## Example 4: Mixed Content
```
# Project Status
## Current Tasks
- Review **API documentation**
- Test `authentication` flow
- Update *user interface*

## Notes
The new system uses `JWT tokens` for **secure** authentication.
```

## Example 5: Simple Note
```
# Meeting Notes
**Date:** Today
**Attendees:** Team members

## Action Items
- Fix the `login bug`
- Update **documentation**
- Schedule *next meeting*
```

## How to Test:
1. Open the infinite canvas application
2. Double-click to create a new node
3. Copy one of the examples above
4. Paste it into the node
5. Press Enter or click outside to finish editing
6. The node should automatically resize and display formatted text!

## Features Supported:
- ✅ Headers (# ## ###)
- ✅ Bold (**text**)
- ✅ Italic (*text*)
- ✅ Code (`text`)
- ✅ Lists (- item)
- ✅ Automatic node sizing
- ✅ Mixed formatting
