<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Large Text Generator for Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        .container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        button {
            background-color: #0d7377;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #14a085;
        }
        
        textarea {
            width: 100%;
            height: 300px;
            background-color: #1a1a1a;
            color: #e0e0e0;
            border: 1px solid #666;
            border-radius: 5px;
            padding: 10px;
            font-family: Arial, sans-serif;
            resize: vertical;
        }
        
        .info {
            margin-top: 10px;
            font-size: 14px;
            color: #9ca3af;
        }
    </style>
</head>
<body>
    <h1>Large Text Generator for HumOS Canvas Testing</h1>
    
    <div class="container">
        <h2>Generate Test Content</h2>
        <button onclick="generateSmallText()">Small Text (~500 words)</button>
        <button onclick="generateMediumText()">Medium Text (~2,000 words)</button>
        <button onclick="generateLargeText()">Large Text (~10,000 words)</button>
        <button onclick="generateHugeText()">Huge Text (~20,000 words)</button>
        <button onclick="generateMarkdownText()">Markdown Sample</button>
        
        <textarea id="generatedText" placeholder="Generated text will appear here..."></textarea>
        <div class="info">
            <span id="wordCount">Word count: 0</span> | 
            <span id="charCount">Character count: 0</span>
        </div>
        
        <button onclick="copyToClipboard()">Copy to Clipboard</button>
        <button onclick="clearText()">Clear</button>
    </div>
    
    <div class="container">
        <h2>Instructions</h2>
        <ol>
            <li>Click one of the generate buttons above to create test content</li>
            <li>Copy the generated text to clipboard</li>
            <li>Open HumOS Canvas in another tab</li>
            <li>Double-click to create a new node</li>
            <li>Paste the large text content</li>
            <li>Test scrolling with mouse wheel over the node</li>
        </ol>
    </div>

    <script>
        function updateCounts() {
            const text = document.getElementById('generatedText').value;
            const words = text.trim() ? text.trim().split(/\s+/).length : 0;
            const chars = text.length;
            
            document.getElementById('wordCount').textContent = `Word count: ${words}`;
            document.getElementById('charCount').textContent = `Character count: ${chars}`;
        }
        
        function generateSmallText() {
            const text = generateLoremIpsum(500);
            document.getElementById('generatedText').value = text;
            updateCounts();
        }
        
        function generateMediumText() {
            const text = generateLoremIpsum(2000);
            document.getElementById('generatedText').value = text;
            updateCounts();
        }
        
        function generateLargeText() {
            const text = generateLoremIpsum(10000);
            document.getElementById('generatedText').value = text;
            updateCounts();
        }
        
        function generateHugeText() {
            const text = generateLoremIpsum(20000);
            document.getElementById('generatedText').value = text;
            updateCounts();
        }
        
        function generateMarkdownText() {
            const text = `# Large Document with Markdown

## Introduction
This is a comprehensive document designed to test the scrollable content functionality in HumOS Canvas. It contains various markdown elements and a substantial amount of text to ensure the scrolling mechanism works properly.

### Key Features Being Tested
- **Scrollable content**: Nodes should maintain a maximum height
- **Mouse wheel scrolling**: Users should be able to scroll through content
- **Visual indicators**: Scrollbars should appear when content overflows
- **Performance**: Smooth scrolling even with large amounts of text

## Chapter 1: Lorem Ipsum Content

${generateLoremIpsum(3000)}

## Chapter 2: Technical Specifications

### System Requirements
- Modern web browser with HTML5 Canvas support
- JavaScript enabled
- Mouse or trackpad for interaction

### Implementation Details
The scrollable content system works by:
1. Calculating the total content height
2. Comparing it to the maximum node height
3. Rendering only the visible portion
4. Providing scroll controls for navigation

${generateLoremIpsum(2000)}

## Chapter 3: User Experience

### Interaction Patterns
Users can interact with scrollable content in several ways:
- **Mouse wheel**: Scroll up and down through content
- **Visual feedback**: Scrollbar indicates position and available content
- **Smooth rendering**: Content appears seamlessly as you scroll

${generateLoremIpsum(4000)}

## Chapter 4: Performance Considerations

### Optimization Strategies
- Only render visible content
- Use efficient clipping regions
- Minimize redraw operations
- Cache calculated heights

${generateLoremIpsum(3000)}

## Conclusion

This document demonstrates the scrollable content functionality with approximately 15,000+ words of content. The system should handle this smoothly while maintaining good performance and user experience.

${generateLoremIpsum(1000)}`;
            
            document.getElementById('generatedText').value = text;
            updateCounts();
        }
        
        function generateLoremIpsum(wordCount) {
            const words = [
                'lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit',
                'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore',
                'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud',
                'exercitation', 'ullamco', 'laboris', 'nisi', 'aliquip', 'ex', 'ea', 'commodo',
                'consequat', 'duis', 'aute', 'irure', 'in', 'reprehenderit', 'voluptate',
                'velit', 'esse', 'cillum', 'fugiat', 'nulla', 'pariatur', 'excepteur', 'sint',
                'occaecat', 'cupidatat', 'non', 'proident', 'sunt', 'culpa', 'qui', 'officia',
                'deserunt', 'mollit', 'anim', 'id', 'est', 'laborum', 'at', 'vero', 'eos',
                'accusamus', 'accusantium', 'doloremque', 'laudantium', 'totam', 'rem',
                'aperiam', 'eaque', 'ipsa', 'quae', 'ab', 'illo', 'inventore', 'veritatis'
            ];
            
            let result = [];
            for (let i = 0; i < wordCount; i++) {
                result.push(words[Math.floor(Math.random() * words.length)]);
                
                // Add punctuation occasionally
                if (i > 0 && i % 15 === 0) {
                    result[result.length - 1] += '.';
                    if (i % 60 === 0) {
                        result.push('\n\n');
                    }
                } else if (i > 0 && i % 8 === 0) {
                    result[result.length - 1] += ',';
                }
            }
            
            return result.join(' ').replace(/\s+/g, ' ').trim();
        }
        
        function copyToClipboard() {
            const textarea = document.getElementById('generatedText');
            textarea.select();
            document.execCommand('copy');
            
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        }
        
        function clearText() {
            document.getElementById('generatedText').value = '';
            updateCounts();
        }
        
        // Update counts when text changes
        document.getElementById('generatedText').addEventListener('input', updateCounts);
    </script>
</body>
</html>
