<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="humanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4a90e2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357abd;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb347;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="synergyGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.4" />
      <stop offset="70%" style="stop-color:#ffffff;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0" />
    </radialGradient>
    <radialGradient id="coreGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="50%" style="stop-color:#e6f3ff;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#4a90e2;stop-opacity:0.3" />
    </radialGradient>
  </defs>
  
  <!-- Background circle with subtle gradient -->
  <circle cx="64" cy="64" r="60" fill="#1a1a1a" stroke="url(#humanGradient)" stroke-width="4"/>
  
  <!-- Human brain/consciousness representation (left side) -->
  <path d="M24 64c0-16 12-28 28-28 8 0 15.2 4 20 10" fill="none" stroke="url(#humanGradient)" stroke-width="6" stroke-linecap="round"/>
  <path d="M32 52c4-4 10-6 16-6" fill="none" stroke="url(#humanGradient)" stroke-width="4" stroke-linecap="round" opacity="0.7"/>
  <path d="M28 58c2-2 5-3 8-3" fill="none" stroke="url(#humanGradient)" stroke-width="3" stroke-linecap="round" opacity="0.5"/>
  
  <!-- AI neural network representation (right side) -->
  <path d="M104 64c0 16-12 28-28 28-8 0-15.2-4-20-10" fill="none" stroke="url(#aiGradient)" stroke-width="6" stroke-linecap="round"/>
  <path d="M96 76c-4 4-10 6-16 6" fill="none" stroke="url(#aiGradient)" stroke-width="4" stroke-linecap="round" opacity="0.7"/>
  <path d="M100 70c-2 2-5 3-8 3" fill="none" stroke="url(#aiGradient)" stroke-width="3" stroke-linecap="round" opacity="0.5"/>
  
  <!-- Central synergy core - the integration point -->
  <circle cx="64" cy="64" r="12" fill="url(#synergyGlow)" stroke="#ffffff" stroke-width="2" opacity="0.9"/>
  <circle cx="64" cy="64" r="6" fill="url(#coreGlow)"/>
  <circle cx="64" cy="64" r="3" fill="#ffffff" opacity="0.9"/>
  
  <!-- Data flow/connection lines showing continuous loop -->
  <path d="M52 56 Q64 44 76 56" fill="none" stroke="#4a90e2" stroke-width="3" opacity="0.6" stroke-dasharray="6,4"/>
  <path d="M76 72 Q64 84 52 72" fill="none" stroke="#ffd700" stroke-width="3" opacity="0.6" stroke-dasharray="6,4"/>
  
  <!-- Secondary flow lines for depth -->
  <path d="M48 60 Q64 50 80 60" fill="none" stroke="#4a90e2" stroke-width="2" opacity="0.4" stroke-dasharray="4,3"/>
  <path d="M80 68 Q64 78 48 68" fill="none" stroke="#ffd700" stroke-width="2" opacity="0.4" stroke-dasharray="4,3"/>
  
  <!-- Enhancement nodes - representing augmented capabilities -->
  <circle cx="40" cy="40" r="6" fill="url(#humanGradient)" opacity="0.8"/>
  <circle cx="88" cy="40" r="6" fill="url(#aiGradient)" opacity="0.8"/>
  <circle cx="40" cy="88" r="6" fill="url(#humanGradient)" opacity="0.8"/>
  <circle cx="88" cy="88" r="6" fill="url(#aiGradient)" opacity="0.8"/>
  
  <!-- Additional capability nodes -->
  <circle cx="20" cy="64" r="4" fill="url(#humanGradient)" opacity="0.6"/>
  <circle cx="108" cy="64" r="4" fill="url(#aiGradient)" opacity="0.6"/>
  <circle cx="64" cy="20" r="4" fill="#ffffff" opacity="0.6"/>
  <circle cx="64" cy="108" r="4" fill="#ffffff" opacity="0.6"/>
  
  <!-- Connection lines to enhancement nodes -->
  <line x1="52" y1="52" x2="40" y2="40" stroke="#4a90e2" stroke-width="2" opacity="0.4"/>
  <line x1="76" y1="52" x2="88" y2="40" stroke="#ffd700" stroke-width="2" opacity="0.4"/>
  <line x1="52" y1="76" x2="40" y2="88" stroke="#4a90e2" stroke-width="2" opacity="0.4"/>
  <line x1="76" y1="76" x2="88" y2="88" stroke="#ffd700" stroke-width="2" opacity="0.4"/>
  
  <!-- Subtle connections to edge nodes -->
  <line x1="52" y1="64" x2="20" y2="64" stroke="#4a90e2" stroke-width="1.5" opacity="0.3"/>
  <line x1="76" y1="64" x2="108" y2="64" stroke="#ffd700" stroke-width="1.5" opacity="0.3"/>
  <line x1="64" y1="52" x2="64" y2="20" stroke="#ffffff" stroke-width="1.5" opacity="0.3"/>
  <line x1="64" y1="76" x2="64" y2="108" stroke="#ffffff" stroke-width="1.5" opacity="0.3"/>
</svg>
